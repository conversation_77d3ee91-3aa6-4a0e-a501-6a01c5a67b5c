import type {
  CreatePrdParams,
  PrdDetail,
  PrdFormData,
  PrdItem,
  PrdListParams,
  UpdatePrdParams,
} from "-/prd";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";
import { createPRDSSEConnection } from "~/pages/prdAgent/utils/sse-helper";
import { useUserStore } from "~/store/modules/user";

/**
 * PRD相关API路径枚举
 */
enum API {
  // PRD列表
  PrdList = "/v1/prd/projects",
  // PRD
  PrdApi = "/v1/prd/projects/{id}",
  // 更新PRD
  UpdatePrd = "/api/v1/prd/updateProjects/{id}",
  // prd重新生成
  RegeneratePrd = "/api/v1/prd/projects/{id}/generate-prd",
  // PRD生成接口（EventSource）
  GeneratePrd = "/api/v1/prd/projects",
  // PRD生成指定类型文档
  PrdGenerateChart = "/api/v1/prd/projects/{id}/generate",
}

/**
 * 获取PRD列表
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PrdItem[]> PRD列表
 */
export function getPrdList(params: PrdListParams = {}, errorMessageMode: MessageMode = "message"): Promise<PrdItem[]> {
  return request.get({ url: API.PrdList, params }, { errorMessageMode });
}

/**
 * 获取PRD详情
 * @param id - PRD ID
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PrdDetail> PRD详情
 */
export function getPrdDetail(id: string, errorMessageMode: MessageMode = "message"): Promise<PrdDetail> {
  const url = replaceUrlPathParams(API.PrdApi, { id });
  return request.get({ url }, { errorMessageMode });
}

/**
 * PRD生成指定类型文档
 *
 */
export function generatePrdChart(id: string, data, callbacks: {
  onStart?: () => void;
  onMessage?: (content: string) => void;
  onCompleted?: () => void;
  onError?: (error: any) => void;
}): { disconnect: () => void } {
  const url = replaceUrlPathParams(API.PrdGenerateChart, { id });
  // 获取当前租户ID
  const userStore = useUserStore();
  const currentTenant = userStore.getCurrentTenant;
  const tenantId = currentTenant?.id || currentTenant?.tenant_id;

  // 构建请求头
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (tenantId) {
    headers["x-done-tenant"] = tenantId;
  }

  // 创建SSE连接
  const sseManager = createPRDSSEConnection(
    {
      url,
      method: "POST", // 使用项目的SseClient，支持POST请求
      headers,
      body: data,
      timeout: 60000, // 60秒超时
      retryAttempts: 2,
      retryDelay: 2000,
    },
    {
      onMessage: (data) => {
        if (data.answer) {
          callbacks.onMessage?.(data.answer);
        }
      },
      onMessageEnd: () => {
        callbacks.onCompleted?.();
      },
      onUnknownEvent: (eventType, data) => {
        console.warn("未知SSE事件:", eventType, data);
      },
    },
    {
      onStateChange: (state) => {
        if (state === "connected") {
          callbacks.onStart?.();
        }
      },
      onError: (error) => {
        callbacks.onError?.(error);
      },
      onTimeout: () => {
        callbacks.onError?.(new Error("连接超时"));
      },
    },
  );

  // 建立连接
  sseManager.connect();

  // 返回控制接口
  return {
    disconnect: () => sseManager.disconnect(),
  };
}


/**
 * 生成PRD（使用SSE流式传输）
 * @param formData - 表单数据
 * @param callbacks - 回调函数
 * @param callbacks.onStart - 开始回调
 * @param callbacks.onProjectCreated - 项目创建回调
 * @param callbacks.onMessage - 消息回调
 * @param callbacks.onCompleted - 完成回调
 * @param callbacks.onError - 错误回调
 * @param projectId - 可选的项目ID，如果提供则更新现有项目，否则创建新项目
 */
export function generatePrdStream(
  formData: PrdFormData,
  callbacks: {
    onStart?: () => void;
    onProjectCreated?: (projectId: string) => void;
    onMessage?: (content: string) => void;
    onCompleted?: () => void;
    onError?: (error: any) => void;
  },
  projectId?: string,
  isRegenerate?: boolean,
): { disconnect: () => void } {
  // 获取当前租户ID
  const userStore = useUserStore();
  const currentTenant = userStore.getCurrentTenant;
  const tenantId = currentTenant?.id || currentTenant?.tenant_id;

  // 构建请求头
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (tenantId) {
    headers["x-done-tenant"] = tenantId;
  }

  // 根据是否有项目ID决定URL、HTTP方法和请求体
  let apiUrl: string = API.GeneratePrd;

  if (projectId) {
    // 更新现有项目，使用PUT方法和包含ID的URL
    apiUrl = replaceUrlPathParams(API.UpdatePrd, { id: projectId });
  }

  if (isRegenerate) {
    apiUrl = replaceUrlPathParams(API.RegeneratePrd, { id: projectId });
  }

  // 创建SSE连接
  const sseManager = createPRDSSEConnection(
    {
      url: apiUrl,
      method: "POST",
      headers,
      body: formData,
      timeout: 60000, // 60秒超时
      retryAttempts: 2,
      retryDelay: 2000,
    },
    {
      onProjectCreated: (data) => {
        if (data.data?.id) {
          callbacks.onProjectCreated?.(data.data.id);
        }
      },
      onMessage: (data) => {
        if (data.answer) {
          callbacks.onMessage?.(data.answer);
        }
      },
      onMessageEnd: () => {
        callbacks.onCompleted?.();
      },
      onUnknownEvent: (eventType, data) => {
        console.warn("未知SSE事件:", eventType, data);
      },
    },
    {
      onStateChange: (state) => {
        if (state === "connected") {
          callbacks.onStart?.();
        }
      },
      onError: (error) => {
        callbacks.onError?.(error);
      },
      onTimeout: () => {
        callbacks.onError?.(new Error("连接超时"));
      },
    },
  );

  // 建立连接
  sseManager.connect();

  // 返回控制接口
  return {
    disconnect: () => sseManager.disconnect(),
  };
}
