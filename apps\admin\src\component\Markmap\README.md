# Markmap 思维导图组件

基于 `markmap-lib` 和 `markmap-view` 的 Vue 3 思维导图组件，支持将 Markdown 内容转换为交互式思维导图。

## 特性

- ✅ **响应式更新**：支持 Markdown 内容变化时自动重新渲染
- ✅ **防抖优化**：使用 lodash debounce 优化频繁更新
- ✅ **错误处理**：完善的错误处理和用户反馈
- ✅ **加载状态**：提供加载、空状态、错误状态的 UI 反馈
- ✅ **TypeScript 支持**：完整的类型定义
- ✅ **Tailwind CSS 样式**：使用 Tailwind CSS 进行样式设计
- ✅ **方法暴露**：暴露常用方法供父组件调用

## 安装依赖

组件依赖以下包（项目中已安装）：

```json
{
  "markmap-lib": "^0.18.12",
  "markmap-view": "^0.18.12",
  "lodash": "^4.x.x"
}
```

## 基础用法

```vue
<template>
  <div class="h-96">
    <Markmap
      :content="markdownContent"
      @ready="handleReady"
      @error="handleError"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from "vue";
  import { Markmap } from "~/component/Markmap";

  const markdownContent = ref(`
# 主题
## 子主题 1
- 要点 1
- 要点 2
## 子主题 2
- 要点 3
- 要点 4
`);

  function handleReady(ready: boolean) {
    console.log("思维导图就绪:", ready);
  }

  function handleError(error: Error) {
    console.error("思维导图错误:", error);
  }
</script>
```

## Props

| 属性            | 类型      | 默认值 | 说明                 |
| --------------- | --------- | ------ | -------------------- |
| `content`       | `string`  | `""`   | Markdown 内容        |
| `autoFit`       | `boolean` | `true` | 是否自动适应容器大小 |
| `debounceDelay` | `number`  | `300`  | 防抖延迟时间（毫秒） |

## Events

| 事件名  | 参数               | 说明                     |
| ------- | ------------------ | ------------------------ |
| `ready` | `(ready: boolean)` | 思维导图初始化完成时触发 |
| `error` | `(error: Error)`   | 思维导图渲染出错时触发   |

## 暴露的方法

通过 `ref` 可以访问以下方法：

```vue
<template>
  <Markmap ref="markmapRef" :content="content" />
</template>

<script setup lang="ts">
  import { ref } from "vue";

  const markmapRef = ref();

  // 重新适应容器大小
  function fitToContainer() {
    markmapRef.value?.fit();
  }

  // 重新初始化
  function refresh() {
    markmapRef.value?.refresh();
  }

  // 清空思维导图
  function clear() {
    markmapRef.value?.clear();
  }

  // 获取 Markmap 实例
  function getInstance() {
    return markmapRef.value?.getInstance();
  }
</script>
```

## 样式定制

组件使用 Tailwind CSS 进行样式设计，主要的样式类包括：

- 容器：`relative w-full h-full min-h-96 bg-white rounded-lg border border-gray-200`
- 加载状态：`animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500`
- 空状态：`text-gray-400`
- 错误状态：`text-red-400`

### 容器尺寸问题

```vue
<template>
  <!-- 确保容器有明确的高度 -->
  <div class="h-96 w-full">
    <Markmap :content="content" />
  </div>
</template>
```

## 注意事项

1. **容器高度**：确保为组件提供明确的高度，组件会自动填充容器
2. **Markdown 格式**：确保 Markdown 内容格式正确，特别是标题层级
3. **性能优化**：组件内置防抖机制，避免频繁更新导致的性能问题
4. **错误处理**：监听 `error` 事件来处理渲染错误
5. **文字方向**：组件已优化文字显示方向，确保水平显示

## 示例

查看 `example/MarkmapExample.vue` 文件获取完整的使用示例。
