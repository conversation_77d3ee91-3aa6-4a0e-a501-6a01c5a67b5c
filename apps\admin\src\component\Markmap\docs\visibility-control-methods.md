# Markmap 组件可见性控制方案

本文档展示了控制 Markmap 组件可见性的多种实现方案，每种方案都有其优缺点和适用场景。

## 方案1：v-show 指令（当前使用）

```vue
<svg
  v-show="isReady && !hasError"
  ref="markmapContainer"
  class="w-full h-full"
></svg>
```

**优点：**
- 简洁明了，易于理解
- 元素始终存在于 DOM 中，只是控制 display 属性
- 切换性能好，适合频繁显示/隐藏

**缺点：**
- 元素仍占用 DOM 空间
- 不如透明度控制平滑

## 方案2：v-if 条件渲染

```vue
<svg
  v-if="isReady && !hasError"
  ref="markmapContainer"
  class="w-full h-full"
></svg>
```

**优点：**
- 完全从 DOM 中移除/添加元素
- 节省内存，不渲染不需要的元素

**缺点：**
- 每次切换都会重新创建/销毁元素
- 可能导致 Markmap 实例丢失，需要重新初始化

## 方案3：CSS 类控制

```vue
<template>
  <svg
    ref="markmapContainer"
    class="w-full h-full"
    :class="visibilityClass"
  ></svg>
</template>

<script setup>
const visibilityClass = computed(() => ({
  'markmap-visible': isReady.value && !hasError.value,
  'markmap-hidden': !isReady.value || hasError.value
}));
</script>

<style scoped>
.markmap-visible {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease-in-out;
}

.markmap-hidden {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out;
}
</style>
```

**优点：**
- 支持过渡动画效果
- 可以精确控制显示/隐藏的动画
- 元素保持在 DOM 中

**缺点：**
- 代码相对复杂
- 需要额外的 CSS 样式

## 方案4：内联样式控制

```vue
<svg
  ref="markmapContainer"
  class="w-full h-full"
  :style="{ 
    visibility: (isReady && !hasError) ? 'visible' : 'hidden',
    opacity: (isReady && !hasError) ? 1 : 0,
    transition: 'opacity 0.3s ease-in-out'
  }"
></svg>
```

**优点：**
- 支持动画过渡
- 不需要额外的 CSS 类
- 逻辑集中在模板中

**缺点：**
- 内联样式较长，可读性稍差
- 不如 CSS 类灵活

## 方案5：计算属性 + 样式对象

```vue
<template>
  <svg
    ref="markmapContainer"
    class="w-full h-full"
    :style="containerStyle"
  ></svg>
</template>

<script setup>
const containerStyle = computed(() => ({
  visibility: (isReady.value && !hasError.value) ? 'visible' : 'hidden',
  opacity: (isReady.value && !hasError.value) ? 1 : 0,
  transition: 'opacity 0.3s ease-in-out, visibility 0.3s ease-in-out'
}));
</script>
```

**优点：**
- 逻辑清晰，易于维护
- 支持复杂的样式计算
- 支持动画效果

**缺点：**
- 需要额外的计算属性

## 方案6：组合多个状态控制

```vue
<template>
  <div class="markmap-container" :class="containerStateClass">
    <svg
      ref="markmapContainer"
      class="w-full h-full markmap-svg"
    ></svg>
  </div>
</template>

<script setup>
const containerStateClass = computed(() => ({
  'markmap-loading': !isReady.value && !hasError.value,
  'markmap-ready': isReady.value && !hasError.value,
  'markmap-error': hasError.value
}));
</script>

<style scoped>
.markmap-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.markmap-svg {
  transition: all 0.3s ease-in-out;
}

.markmap-loading .markmap-svg {
  opacity: 0;
  transform: scale(0.95);
}

.markmap-ready .markmap-svg {
  opacity: 1;
  transform: scale(1);
}

.markmap-error .markmap-svg {
  opacity: 0;
  filter: grayscale(100%);
}
</style>
```

**优点：**
- 支持多种状态的精细控制
- 可以为不同状态添加不同的视觉效果
- 扩展性强

**缺点：**
- 代码复杂度较高
- 需要更多的 CSS 样式

## 推荐方案

根据不同的使用场景，推荐如下：

1. **简单场景**：使用 `v-show`（当前方案）
2. **需要动画效果**：使用方案3或方案5
3. **复杂状态管理**：使用方案6
4. **性能敏感**：使用 `v-if`

当前组件使用 `v-show` 方案，简洁高效，适合大多数使用场景。
