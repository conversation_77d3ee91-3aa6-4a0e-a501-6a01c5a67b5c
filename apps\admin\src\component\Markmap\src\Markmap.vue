<template>
  <svg ref="markmapContainer" class="w-full h-full"></svg>
</template>

<script setup lang="ts">

  import { Markmap } from "markmap-view";
  import { computed, nextTick, onMounted, ref, watch } from "vue";
  import { useMessage } from "naive-ui";
  import { debounce } from "lodash";

  // 初始化 Markmap
  async function initMarkmap() {
    if (!markmapContainer.value || !markdownContent.value) {
      return;
    }

    isReady.value = false;

    try {
      // 动态导入 Markmap
      const { Markmap } = await import("markmap-view");
      const { Transformer } = await import("markmap-lib");

      // 转换 Markdown
      const transformer = new Transformer();
      const { root } = transformer.transform(markdownContent.value);

      // 清空容器
      markmapContainer.value.innerHTML = "";

      // 创建 Markmap 实例
      const mm = Markmap.create(markmapContainer.value);

      // 渲染数据
      mm.setData(root);
      mm.fit();

      isReady.value = true;
    } catch (error) {
      console.error("Markmap 初始化失败:", error);
      isReady.value = false;
    }
  }

</script>
