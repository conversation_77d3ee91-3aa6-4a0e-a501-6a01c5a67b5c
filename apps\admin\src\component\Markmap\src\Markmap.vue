<template>
  <div class="relative w-full h-full min-h-96 bg-white rounded-lg border border-gray-200 overflow-hidden">
    <!-- 加载状态 -->
    <div
      v-if="!isReady && markdownContent"
      class="absolute inset-0 flex items-center justify-center bg-gray-50 z-10"
    >
      <div class="flex flex-col items-center space-y-3">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span class="text-sm text-gray-600">正在生成思维导图...</span>
      </div>
    </div>

    <!-- 空状态 -->
    <div
      v-if="!markdownContent"
      class="absolute inset-0 flex items-center justify-center text-gray-400"
    >
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="mt-2 text-sm">
          暂无 Markdown 内容
        </p>
        <p class="text-xs text-gray-300">
          请提供 Markdown 内容以生成思维导图
        </p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div
      v-if="hasError"
      class="absolute inset-0 flex items-center justify-center text-red-400"
    >
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <p class="mt-2 text-sm">
          思维导图生成失败
        </p>
        <p class="text-xs text-red-300">
          请检查 Markdown 格式是否正确
        </p>
      </div>
    </div>

    <!-- Markmap 容器 -->
    <svg
      ref="markmapContainer"
      class="w-full h-full"
      :style="{
        opacity: (isReady && !hasError) ? 1 : 0,
        transition: 'opacity 0.3s ease-in-out',
      }"
    ></svg>
  </div>
</template>

<script setup lang="ts">
  import { debounce } from "lodash";
  import { useMessage } from "naive-ui";
  import { nextTick, onMounted, ref, watch } from "vue";

  defineOptions({
    name: "Markmap",
    inheritAttrs: false,
  });

  // Props 定义
  const props = withDefaults(defineProps<{
    /** Markdown 内容 */
    content?: string;
    /** 是否自动适应容器大小 */
    autoFit?: boolean;
    /** 防抖延迟时间（毫秒） */
    debounceDelay?: number;
  }>(), {
    content: "",
    autoFit: true,
    debounceDelay: 300,
  });

  // Emits 定义
  const emit = defineEmits<{
    /** 思维导图初始化完成 */
    ready: [ready: boolean];
    /** 思维导图渲染出错 */
    error: [error: Error];
  }>();

  // 响应式数据
  const markmapContainer = ref<HTMLElement>();
  const markdownContent = ref(props.content);
  const isReady = ref(false);
  const hasError = ref(false);
  const markmapInstance = ref<any>(null);

  // 消息提示
  const message = useMessage();

  // 监听 props.content 变化
  watch(
    () => props.content,
    (newContent) => {
      markdownContent.value = newContent;
      hasError.value = false;
    },
    { immediate: true },
  );

  // 防抖的初始化函数
  const debouncedInitMarkmap = debounce(initMarkmap, props.debounceDelay);

  // 监听 markdownContent 变化，防抖更新
  watch(
    markdownContent,
    (newContent) => {
      if (newContent) {
        debouncedInitMarkmap();
      } else {
        clearMarkmap();
      }
    },
    { immediate: false },
  );

  // 初始化 Markmap
  async function initMarkmap() {
    if (!markmapContainer.value || !markdownContent.value) {
      return;
    }

    isReady.value = false;
    hasError.value = false;

    try {
      // 动态导入 Markmap
      const { Markmap } = await import("markmap-view");
      const { Transformer } = await import("markmap-lib");

      // 创建 Transformer 实例
      const transformer = new Transformer();
      const { root } = transformer.transform(markdownContent.value);

      // 清空容器
      markmapContainer.value.innerHTML = "";

      // 创建 Markmap 实例，配置选项
      markmapInstance.value = Markmap.create(markmapContainer.value);

      // 渲染数据
      markmapInstance.value.setData(root);

      // 等待下一个 tick 后自适应大小
      await nextTick();
      if (props.autoFit) {
        markmapInstance.value.fit();
      }

      isReady.value = true;
      emit("ready", true);
    } catch (error) {
      console.error("Markmap 初始化失败:", error);
      hasError.value = true;
      isReady.value = false;

      const errorMessage = error instanceof Error ? error.message : "未知错误";
      message.error(`思维导图生成失败: ${errorMessage}`);
      emit("error", error instanceof Error ? error : new Error(String(error)));
    }
  }

  // 清空 Markmap
  function clearMarkmap() {
    if (markmapContainer.value) {
      markmapContainer.value.innerHTML = "";
    }
    markmapInstance.value = null;
    isReady.value = false;
    hasError.value = false;
  }

  // 重新适应大小
  function fitToContainer() {
    if (markmapInstance.value && isReady.value) {
      markmapInstance.value.fit();
    }
  }

  // 暴露给父组件的方法
  defineExpose({
    /** 重新适应容器大小 */
    fit: fitToContainer,
    /** 重新初始化 */
    refresh: initMarkmap,
    /** 清空思维导图 */
    clear: clearMarkmap,
    /** 获取 Markmap 实例 */
    getInstance: () => markmapInstance.value,
  });

  // 组件挂载后初始化
  onMounted(() => {
    if (markdownContent.value) {
      initMarkmap();
    }
  });
</script>
