<template>
  <div>
    <MdPreview :id="id" :model-value="props.text" />
    <MdCatalog v-if="props.isShowLogo" :editor-id="id" :scroll-element="scrollElement" />
  </div>
</template>

<script setup lang="ts">
  import { config, MdCatalog, MdPreview } from "md-editor-v3";
  import mermaid from "mermaid";
  import "md-editor-v3/lib/preview.css";

  interface MdPreviewProps {
    text?: string;
    isShowLogo?: boolean;
  }

  defineOptions({
    name: "MdPreview",
  });

  const props = withDefaults(defineProps<MdPreviewProps>(), {
    text: "",
    isShowLogo: false,
  });
  const id = "preview-only";
  const scrollElement = document.documentElement;
  config({
    editorExtensions: {
      mermaid: {
        instance: mermaid,
      },
    },
  });
</script>

<style scoped>
  ::v-deep .vuepress-markdown-body:not(.custom) {
  padding: 0.5rem 1rem;
}
</style>
