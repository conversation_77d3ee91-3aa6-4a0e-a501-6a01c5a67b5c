<template>
  <div>
    <!-- Mermaid 图表容器 -->
    <pre ref="mermaidContainer" class="mermaid"></pre>

    <!-- 下载按钮 -->
    <button @click="downloadImage">
      下载 Mermaid 图表
    </button>
  </div>
</template>

<script setup lang="ts">
  import mermaid from "mermaid";
  // import { onMounted, ref, watch } from "vue";

  const props = defineProps<{
    text: string;
  }>();

  const message = useMessage();
  // Mermaid 图表容器引用
  const mermaidContainer = ref<HTMLDivElement | null>(null);

  // 渲染 Mermaid 图表
  async function renderMermaid() {
    if (mermaidContainer.value) {
      try {
        // 清空容器
        // mermaidContainer.value.innerHTML = "";

        // 渲染 Mermaid 内容
        mermaid.parse(props.text);
        const { svg } = await mermaid.render("mermaid-svg", props.text);
        mermaidContainer.value.innerHTML = svg;
      } catch (error) {
        console.error("Mermaid 渲染失败:", error);
      }
    }
  }

  // 监听 text 的变化，重新渲染图表
  watch(
    () => props.text,
    () => {
      renderMermaid();
    },
    { immediate: true },
  );

  // 下载图表为 PNG
  function downloadImage() {
    const svgElement = mermaidContainer.value?.querySelector("svg");

    if (svgElement) {
      // 将 SVG 转换为 PNG
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const img = new Image();
      img.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgData)}`;

      // 创建 Canvas 元素
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      img.onload = () => {
        // 设置 Canvas 尺寸
        canvas.width = img.width;
        canvas.height = img.height;

        // 将图像绘制到 Canvas 上
        context?.drawImage(img, 0, 0);

        // 下载为 PNG 格式
        const link = document.createElement("a");
        link.href = canvas.toDataURL("image/png");
        link.download = "mermaid_chart.png";
        link.click();
      };
    } else {
      message.error("未找到 Mermaid 图表！");
    }
  }

  // 在组件挂载后渲染 Mermaid 图表
  onMounted(() => {
    mermaid.initialize({
      startOnLoad: false,
      // theme: "base",
      // securityLevel: "loose",
    });
    // mermaid.run();
    renderMermaid();
  });
</script>

<style scoped>
/* 可选：自定义样式 */
/* .mermaid {
  background-color: #f9f9f9;
  padding: 10px;
} */
</style>
