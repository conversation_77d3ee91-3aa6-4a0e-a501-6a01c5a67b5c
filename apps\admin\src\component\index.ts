import type { App } from "vue";
import { ActionIcon, ToolTipper } from "./ActionIcon";
import { AppTable } from "./AppTable";
import { Authority } from "./Authority";
import { DataCard, DataInsightCard } from "./Card";
import { CardModal } from "./CardModal";
import { MdEditor } from "./MdEditor";
import { MdPreview } from "./MdPreview";
import { MermaidChart } from "./MermaidChart";
import { PageWrapper } from "./PageWrapper";
import { QueryHeader } from "./QueryHeader";
import { SearchDialog } from "./SearchDialog";
import { UserSelect } from "./UserSelect";

const components = [
  ActionIcon,
  ToolTipper,
  AppTable,
  Authority,
  DataCard,
  DataInsightCard,
  CardModal,
  PageWrapper,
  SearchDialog,
  UserSelect,
  MdEditor,
  MdPreview,
  QueryHeader,
  MermaidChart,
];

/**
 * 注册所有组件
 * Register all components
 * @param app - Vue应用实例
 * Vue application instance
 */
export function setupComponents(app: App<Element>) {
  components.forEach((component) => {
    app.use(component);
  });
}
