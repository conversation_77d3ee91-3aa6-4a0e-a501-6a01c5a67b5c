<template>
  <PageWrapper :use-default-padding="false">
    <div class="h-full flex flex-col">
      <!-- 页面头部 - 固定在顶部 -->
      <NPageHeader class="p-4 flex-shrink-0 sticky top-0 bg-white z-10 border-b border-gray-200">
        <NFlex class="pr" justify="end">
          <NInputGroup class="w-360px">
            <NInput
              v-model:value="gitUrl"
              placeholder="输入merge_request链接进行代码审查"
              clearable
              @keyup.enter="searchGitUrl"
            />
            <NButton
              type="primary"
              :loading="searchBtnLoading"
              @click="searchGitUrl"
            >
              <template #icon>
                <CAIcon key="search-icon" icon="tabler:search" />
              </template>
              搜索
            </NButton>
          </NInputGroup>

          <NSelect
            v-model:value="userType"
            placeholder="请选择类型"
            :options="userTypeOptions"
            class="w-32"
            @update:value="search"
          />

          <NSelect
            v-model:value="filterType"
            placeholder="请选择类型"
            :options="typeOptions"
            class="w-32"
            @update:value="search"
          />
        </NFlex>
      </NPageHeader>

      <!-- 页面主体 - 可滚动区域 -->
      <NFlex>
        <!-- 内容区域 -->
        <div class="flex-1 overflow-hidden">
          <NSpin :show="dataLoading" class="h-full">
            <div class="h-full overflow-y-auto">
              <div v-if="taskList.length > 0" class="space-y-0">
                <div
                  v-for="(item, index) in taskList"
                  :key="index"
                  class="px-8 py-4 border-b border-gray-100 cursor-pointer transition-colors hover:bg-gray-50"
                  @click="handleReview(item)"
                >
                  <div class="flex items-center mb-2">
                    <span class="flex-1 font-semibold text-gray-900">{{ item.title }}</span>
                  </div>
                  <div class="text-sm text-gray-600 flex items-center gap-2">
                    <span class="task-creator">由 {{ item.author.name }} 创建于</span>
                    <span class="task-create_time">
                      {{ item.created_at }}
                      <CAIcon key="git-branch-icon" icon="tabler:git-branch" class="ml-2" />
                    </span>
                    <span class="task-status">{{ item.target_branch }}</span>
                    <span class="text-gray-300">·</span>
                    <NTag :style="getTypeTag(item.state)">
                      {{ item.stateName }}
                    </NTag>
                  </div>
                </div>
                <!-- 底部留白，避免内容被分页组件遮挡 -->
                <div class="h-20"></div>
              </div>
              <div v-else class="h-full flex items-center justify-center mt-10">
                <NEmpty description="暂无数据" />
              </div>
            </div>
          </NSpin>
        </div>

        <!-- 分页组件 - 绝对定位固定在底部 -->
        <div v-if="taskList.length > 0" class="absolute bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200 flex justify-end z-10 shadow-lg">
          <NPagination
            v-model:page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 30, 50]"
            :item-count="total"
            show-size-picker
            show-quick-jumper
            show-total
            @update:page="handleCurrentChange"
            @update:page-size="handleSizeChange"
          />
        </div>
      </NFlex>

      <TaskDetail ref="taskDetailRef" />
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
  import type { CodeReviewItem, CodeReviewListParams } from "-/codeReview";
  import { CAIcon } from "@celeris/components";
  import { formatToDateTime } from "@celeris/utils";
  import { getCodeReviewList, getGitMessage } from "~/apis/internal/codeReview";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import TaskDetail from "./components/task-detail.vue";
  import { TYPE_LIST, USER_TYPE_LIST } from "./config";

  defineOptions({
    name: "CodeReview",
  });

  // 响应式数据
  const filterType = ref<string>("opened");
  const typeOptions = Object.values(TYPE_LIST).map(item => ({
    label: item.label,
    value: item.value,
  }));
  const userType = ref<number>(1);
  const userTypeOptions = USER_TYPE_LIST.map(item => ({
    label: item.label,
    value: item.value,
  }));
  const taskList = ref<CodeReviewItem[]>([]);
  const currentPage = ref<number>(1);
  const pageSize = ref<number>(10);
  const total = ref<number>(0);
  const dataLoading = ref<boolean>(false);
  const gitUrl = ref<string>("");
  const searchBtnLoading = ref<boolean>(false);

  // 组件引用
  const taskDetailRef = ref<InstanceType<typeof TaskDetail>>();

  // 消息提示
  const message = useMessage();

  // 处理每页显示数量变化
  function handleSizeChange(val: number) {
    pageSize.value = val;
    fetchTaskList();
  }

  // 处理页码变化
  function handleCurrentChange(val: number) {
    currentPage.value = val;
    fetchTaskList();
  }

  // 搜索
  function search() {
    currentPage.value = 1;
    fetchTaskList();
  }

  // 搜索git链接
  async function searchGitUrl() {
    if (!gitUrl.value) {
      message.error("请输入git链接");
      return;
    }

    const params = {
      mergeLink: gitUrl.value,
    };

    searchBtnLoading.value = true;

    try {
      const res = await getGitMessage(params);
      handleReview(res.data);
    } catch (error) {
      message.error("获取git数据失败");
    } finally {
      searchBtnLoading.value = false;
    }
  }

  // 获取任务列表
  async function fetchTaskList() {
    const params: CodeReviewListParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      state: filterType.value,
      isAssign: userType.value,
    };

    dataLoading.value = true;
    taskList.value = [];

    try {
      const res = await getCodeReviewList(params);
      taskList.value = initData(res.items || []);
      total.value = res.total || 0;
    } catch (err) {
      message.error("获取任务列表失败");
    } finally {
      dataLoading.value = false;
    }
  }

  // 处理数据
  function initData(data: CodeReviewItem[]): CodeReviewItem[] {
    return data.map((item) => {
      const typeOption = Object.values(TYPE_LIST).find(option => option.value === item.state);
      item.stateName = typeOption?.label || item.state;
      item.created_at = formatToDateTime(item.created_at);
      return item;
    });
  }

  // 获取标签样式
  function getTypeTag(state: string) {
    return {
      backgroundColor: TYPE_LIST[state].bgColor,
      color: TYPE_LIST[state].textColor,
      border: `1px solid ${TYPE_LIST[state].borderColor}`,
      padding: "2px 8px",
      borderRadius: "4px",
    };
  }

  // 处理任务审核
  function handleReview(item: CodeReviewItem) {
    taskDetailRef.value?.show(item);
  }

  // 组件挂载时获取数据
  onMounted(() => {
    fetchTaskList();
  });
</script>
