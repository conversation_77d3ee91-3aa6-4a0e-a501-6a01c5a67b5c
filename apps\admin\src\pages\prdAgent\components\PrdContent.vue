<template>
  <div :id="id" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col" :style="containerStyle">
    <!-- 标题栏 -->
    <div class="p-2 flex items-center justify-between dark:bg-gray-800/50">
      <div class="text-xl font-bold bg-white dark:bg-gray-800 border-l-4 border-blue-500 text-gray-800 dark:text-gray-200 px-3 py-1 flex items-center tracking-tight">
        {{ title }}
      </div>
      <slot name="button"></slot>
    </div>

    <!-- 内容区域 - 可滚动 -->
    <NFlex>
      <!-- 空状态 -->
      <div v-if="showEmptyState" class="empty-state-container flex-1">
        <div class="empty-state-content">
          <p class="empty-state-text">
            {{ emptyStateText }}
          </p>
        </div>
      </div>

      <!-- 生成状态 -->
      <div v-else-if="showGenerateState" class="generate-state-container flex-1">
        <div class="generate-state-content">
          <NButton type="primary" size="medium" class="generate-button" @click="emits('generate')">
            <template #icon>
              <CAIcon icon="tabler:wand" />
            </template>
            {{ generateStateText }}
          </NButton>
          <p v-if="generateStateDescription" class="generate-state-text">
            {{ generateStateDescription }}
          </p>
        </div>
      </div>

      <!-- 正常内容 -->
      <div v-else class="flex-1">
        <slot></slot>
      </div>
    </NFlex>
  </div>
</template>

<script setup lang="ts">
  import { CAIcon } from "@celeris/components";
  import { NButton } from "naive-ui";
  import { computed } from "vue";

  defineOptions({
    inheritAttrs: false,
  });

  const props = defineProps({
    title: {
      type: String,
      required: true,
    },
    id: {
      type: String,
      required: false,
    },
    height: {
      type: String,
      required: false,
    },
    maxHeight: {
      type: String,
      required: false,
    },
    // 空状态控制
    showEmptyState: {
      type: Boolean,
      default: false,
    },
    emptyStateText: {
      type: String,
      default: "请先生成PRD",
    },
    // 生成状态控制
    showGenerateState: {
      type: Boolean,
      default: false,
    },
    generateStateText: {
      type: String,
      default: "点击生成",
    },
    generateStateDescription: {
      type: String,
      default: "",
    },
    generateStateIcon: {
      type: String,
      default: "tabler:wand",
    },
  });

  const emits = defineEmits<{
    (e: "generate"): void;
  }>();

  const containerStyle = computed(() => {
    const style: Record<string, string> = {};

    if (props.height) {
      style.height = `${props.height} !important`;
      style.flexShrink = "0";
    } else {
      // 没有固定高度时，使用flex布局自适应
      style.flex = "1";
      style.minHeight = "200px";
    }

    if (props.maxHeight) {
      style.maxHeight = props.maxHeight;
    }

    return style;
  });
</script>

<style scoped>
/* 空状态容器样式 */
.empty-state-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  min-height: 200px;
}

.dark .empty-state-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.empty-state-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 10;
}

.empty-state-icon {
  width: 48px;
  height: 48px;
  color: #9ca3af;
  opacity: 0.6;
}

.dark .empty-state-icon {
  color: #6b7280;
}

.empty-state-text {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.dark .empty-state-text {
  color: #9ca3af;
}

/* 生成状态容器样式 */
.generate-state-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  min-height: 200px;
}

.dark .generate-state-container {
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
}

.generate-state-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.generate-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 10;
}

.generate-state-icon {
  width: 64px;
  height: 64px;
  color: #3b82f6;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

.dark .generate-state-icon {
  color: #93c5fd;
}

.generate-button {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  transform: translateY(0);
}

.generate-button:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

.generate-state-text {
  color: #1d4ed8;
  font-size: 12px;
  font-weight: 500;
  margin: 0;
  opacity: 0.8;
}

.dark .generate-state-text {
  color: #bfdbfe;
}

/* 浮动动画 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .empty-state-container,
  .generate-state-container {
    min-height: 100px;
  }

  .empty-state-icon {
    width: 40px;
    height: 40px;
  }

  .generate-state-icon {
    width: 48px;
    height: 48px;
  }

  .generate-state-content {
    gap: 12px;
  }
}
</style>

<style scoped>
/* 自定义滚动条样式 */
:deep(.overflow-y-auto) {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

:deep(.overflow-y-auto):hover {
  scrollbar-color: #555 #f1f1f1;
}
</style>
