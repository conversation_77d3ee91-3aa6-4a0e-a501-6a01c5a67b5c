<template>
  <NModal v-model:show="showModal" :mask-closable="false" preset="card" title="PRD思维导图" class="w-4/5 max-w-6xl">

    <div :style="contentStyle" class="relative">
      <!-- 使用自定义 Markmap 组件 -->
      <svg
        ref="markmapContainer"
        class="w-full h-full"
        style="min-height: 400px;"
      ></svg>
      <div
        v-show="!isReady"
        class="loading-container absolute inset-0 z-10"
      >
        <div class="flex flex-col items-center justify-center gap-3">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span class="text-gray-500">正在生成思维导图...</span>
        </div>
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
  import { NModal, useMessage } from "naive-ui";
  import { computed, nextTick, onMounted, ref, watch } from "vue";

  interface PrdData {
    title?: string;
    content?: string;
  }

  interface Props {
    show: boolean;
    prdData: PrdData;
  }

  interface Emits {
    (e: "update:show", value: boolean): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 组件状态
  const markmapContainer = ref<HTMLElement>();
  const isReady = ref(false);

  // 双向绑定显示状态
  const showModal = computed({
    get: () => props.show,
    set: (value: boolean) => emit("update:show", value),
  });

  // 模态框样式 - 自适应高度
  const contentStyle = computed(() => ({
    height: "70vh",
    minHeight: "400px",
    maxHeight: "800px",
  }));

  // 将 PRD 内容转换为 Markdown 格式
  function convertPrdToMarkdown(content: string): string {
    if (!content) {
      return "";
    }

    const lines = content.split("\n");
    let markdown = "";

    // 添加根节点标题
    const rootTitle = props.prdData.title || "PRD文档";
    markdown += `# ${rootTitle}\n\n`;

    let currentLevel = 1;

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (!trimmedLine) {
        continue;
      }

      // 处理标题层级结构
      if (trimmedLine.startsWith("#") && trimmedLine.includes(" ")) {
        // 一级标题转为二级标题（因为根节点已经是一级）
        const text = trimmedLine.replace(/^#+\s/, "");
        const level = Math.min((trimmedLine.match(/^#+/) || [""])[0].length + 1, 6);
        markdown += `${"#".repeat(level)} ${text}\n\n`;
        currentLevel = level;
      } else if (trimmedLine.match(/^[-*] (.+)$/) || trimmedLine.match(/^\d+\. (.+)$/)) {
        // 处理列表项
        markdown += `${trimmedLine}\n`;
      } else if (trimmedLine.length > 0) {
        // 普通文本处理 - 简化逻辑，避免过度复杂的判断
        if (trimmedLine.length < 30 && !trimmedLine.includes("。") && !trimmedLine.includes("，")) {
          // 短文本可能是子标题
          const level = Math.min(currentLevel + 1, 6);
          markdown += `${"#".repeat(level)} ${trimmedLine}\n\n`;
        } else {
          // 长文本作为列表项，确保有内容显示
          markdown += `- ${trimmedLine}\n`;
        }
      }
    }

    return markdown.trim();
  }

  // 计算 Markdown 内容
  const markdownContent = computed(() => {
    if (!props.prdData.content) {
      return "";
    }
    return convertPrdToMarkdown(props.prdData.content);
  });

  // 初始化 Markmap
  async function initMarkmap() {
    if (!markmapContainer.value || !markdownContent.value) {
      return;
    }

    isReady.value = false;

    try {
      // 动态导入 Markmap
      const { Markmap } = await import("markmap-view");
      const { Transformer } = await import("markmap-lib");

      // 转换 Markdown
      const transformer = new Transformer();
      const { root } = transformer.transform(markdownContent.value);

      // 清空容器
      markmapContainer.value.innerHTML = "";

      // 创建 Markmap 实例
      const mm = Markmap.create(markmapContainer.value);

      // 渲染数据
      mm.setData(root);
      mm.fit();

      isReady.value = true;
    } catch (error) {
      console.error("Markmap 初始化失败:", error);
      isReady.value = false;
    }
  }

  // 监听内容变化
  watch(() => props.prdData.content, () => {
    if (props.show) {
      nextTick(() => initMarkmap());
    }
  });

  // 监听显示状态
  watch(() => props.show, (show) => {
    if (show) {
      nextTick(() => initMarkmap());
    }
  });

  // 组件挂载时初始化
  onMounted(() => {
    if (props.show) {
      nextTick(() => initMarkmap());
    }
  });
</script>

<style scoped>
/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  font-size: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}
</style>
