<template>
  <NModal v-model:show="showModal" :mask-closable="false" preset="card" title="PRD思维导图" class="w-4/5 max-w-6xl">
    <div :style="contentStyle">
      <!-- 使用完善的 Markmap 组件 -->
      <Markmap
        ref="markmapRef"
        :content="markdownContent"
        :auto-fit="true"
        :debounce-delay="500"
        @ready="handleMarkmapReady"
        @error="handleMarkmapError"
      />
    </div>
  </NModal>
</template>

<script setup lang="ts">
  import { NModal, useMessage } from "naive-ui";
  import { computed, ref } from "vue";
  import { Markmap } from "~/component/Markmap";

  interface PrdData {
    title?: string;
    content?: string;
  }

  interface Props {
    show: boolean;
    prdData: PrdData;
  }

  interface Emits {
    (e: "update:show", value: boolean): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 消息提示
  const message = useMessage();

  // 组件引用
  const markmapRef = ref();

  // 双向绑定显示状态
  const showModal = computed({
    get: () => props.show,
    set: (value: boolean) => emit("update:show", value),
  });

  // 模态框样式 - 自适应高度
  const contentStyle = computed(() => ({
    height: "70vh",
    minHeight: "400px",
    maxHeight: "800px",
  }));

  // 将 PRD 内容转换为 Markdown 格式
  function convertPrdToMarkdown(content: string): string {
    if (!content) {
      return "";
    }

    const lines = content.split("\n");
    let markdown = "";

    // 添加根节点标题
    const rootTitle = props.prdData.title || "PRD文档";
    markdown += `# ${rootTitle}\n\n`;

    let currentLevel = 1;

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (!trimmedLine) {
        continue;
      }

      // 处理标题层级结构
      if (trimmedLine.startsWith("#") && trimmedLine.includes(" ")) {
        // 一级标题转为二级标题（因为根节点已经是一级）
        const text = trimmedLine.replace(/^#+\s/, "");
        const level = Math.min((trimmedLine.match(/^#+/) || [""])[0].length + 1, 6);
        markdown += `${"#".repeat(level)} ${text}\n\n`;
        currentLevel = level;
      } else if (trimmedLine.match(/^[-*] (.+)$/) || trimmedLine.match(/^\d+\. (.+)$/)) {
        // 处理列表项
        markdown += `${trimmedLine}\n`;
      } else if (trimmedLine.length > 0) {
        // 普通文本处理 - 简化逻辑，避免过度复杂的判断
        if (trimmedLine.length < 30 && !trimmedLine.includes("。") && !trimmedLine.includes("，")) {
          // 短文本可能是子标题
          const level = Math.min(currentLevel + 1, 6);
          markdown += `${"#".repeat(level)} ${trimmedLine}\n\n`;
        } else {
          // 长文本作为列表项，确保有内容显示
          markdown += `- ${trimmedLine}\n`;
        }
      }
    }

    return markdown.trim();
  }

  // 计算 Markdown 内容
  const markdownContent = computed(() => {
    if (!props.prdData.content) {
      return "";
    }
    return convertPrdToMarkdown(props.prdData.content);
  });

  // Markmap 事件处理
  function handleMarkmapReady(ready: boolean) {
    // 思维导图准备就绪
    if (ready) {
      // 可以在这里添加成功提示或其他逻辑
    }
  }

  function handleMarkmapError(error: Error) {
    console.error("PRD 思维导图生成失败:", error);
    message.error(`思维导图生成失败: ${error.message}`);
  }
</script>
