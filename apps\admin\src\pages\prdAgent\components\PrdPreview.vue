<template>
  <NFlex vertical class="h-full">
    <!-- 头部区域 -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0">
      <!-- 左侧标题 -->
      <div class="flex items-center gap-3">
        <CAIcon icon="tabler:file-text" class="text-blue-600 text-xl" />
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ displayTitle }}的PRD
        </div>
      </div>

      <!-- 右侧导出按钮 -->
      <div class="flex items-center gap-2">
        <NButton type="primary" size="medium" :loading="exportLoading" @click="handleExport">
          <template #icon>
            <CAIcon icon="tabler:download" />
          </template>
          全部导出
        </NButton>
      </div>
    </div>

    <!-- 内容区域 -->
    <NScrollbar ref="scrollbarRef" class="flex-1 pb-18">
      <NFlex vertical :size="16">
        <!-- PRD正文 - 使用独立的PrdMainContent组件 -->
        <PrdMainContent ref="mainContentRef" :prd-data="prdData" @regenerate="handleMainContentRegenerate" />

        <!-- 动态渲染其他PrdContent组件 -->
        <PrdContent
          v-for="config in contentConfigs"
          :id="config.id"
          :key="config.id"
          :title="config.title"
          :show-empty-state="!prdData.content"
          :show-generate-state="!!prdData.content && !sections[config.sectionKey] && !loadingState[config.sectionKey]"
          :generate-state-icon="config.icon"
          :generate-state-description="config.description"
          @generate="() => handleGenerate(config.title, config.sectionKey)"
        >
          <!-- :max-height="sections[config.sectionKey] ? '300px' : undefined" -->
          <template v-if="config.showExportButton && prdData.content && sections[config.sectionKey]?.length && !loadingState[config.sectionKey]" #button>
            <NFlex>
              <NButton size="small" type="primary" ghost @click="() => handleGenerate(config.title, config.sectionKey)">
                <template #icon>
                  <CAIcon icon="tabler:refresh" />
                </template>
                重新生成
              </NButton>
              <NButton size="small" type="primary" ghost @click="() => handleSectionExport(config.title, config.sectionKey)">
                <template #icon>
                  <CAIcon icon="tabler:download" />
                </template>
                导出
              </NButton>
            </NFlex>
          </template>

          <div v-if="sections[config.sectionKey]" class="p-4 pt-0">
            <div v-if="config.dataType === 'Mermaid'">
              <MermaidChart :text="sections[config.sectionKey]" />
            </div>
            <div v-else>
              <MdPreview :text="sections[config.sectionKey]" />
            </div>
          </div>
        </PrdContent>
      </NFlex>
    </NScrollbar>
  </NFlex>
</template>

<script setup lang="ts">
  import { CAIcon } from "@celeris/components";
  // 移除未使用的导入，统一使用 ~/utils/markdownToWord 中的工具函数
  // import { convertHtmlToDocx, convertMarkdownToHtml } from "@celeris/utils";
  import { NButton, NScrollbar } from "naive-ui";
  import { onBeforeUnmount } from "vue";
  import { generatePrdChart } from "~/apis/internal/prd";
  import { MdPreview } from "~/component/MdPreview";
  import { MermaidChart } from "~/component/MermaidChart";
  import { exportMarkdownToWord } from "~/utils/markdownToWord";
  import { contentConfigs } from "../config";
  import PrdContent from "./PrdContent.vue";
  import PrdMainContent from "./PrdMainContent.vue";

  // PRD数据结构
  interface PrdData {
    title: string;
    content: string;
  }

  // Props定义
  interface Props {
    title?: string;
    currentProjectId?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    title: "",
    currentProjectId: "",
  });

  const emits = defineEmits<{
    (e: "regenerateMainContent"): void;
  }>();

  // 初始化空数据
  const prdData = ref<PrdData>({
    title: "",
    content: "",
  });

  const sections = ref<Record<string, string>>({});
  const loadingState = ref<Record<string, boolean>>({});
  const scrollbarRef = ref<InstanceType<typeof NScrollbar>>();
  const mainContentRef = ref<InstanceType<typeof PrdMainContent> | null>(null);
  const exportLoading = ref(false);
  const message = useMessage();

  // 计算显示标题
  const displayTitle = computed(() => {
    return props.title || prdData.value.title || "PRD文档";
  });

  // 导出功能 - 基于 handleSectionExport 的设计模式重构
  function handleExport() {
    if (!prdData.value.content && Object.keys(sections.value).length === 0) {
      message.warning("暂无内容可导出");
      return;
    }

    exportLoading.value = true;
    try {
      // 构建完整的PRD内容
      let fullContent = "";

      // 添加标题
      const title = displayTitle.value;
      fullContent += `# ${title}\n\n`;

      // 添加PRD正文内容
      if (prdData.value.content) {
        fullContent += `## PRD正文\n\n${prdData.value.content}\n\n`;
      }

      // 添加其他模块内容
      for (const [sectionKey, sectionContent] of Object.entries(sections.value)) {
        if (sectionContent && sectionContent.trim()) {
          fullContent += `## ${sectionKey}\n\n${sectionContent}\n\n`;
        }
      }

      // 使用与 handleSectionExport 相同的工具函数
      exportMarkdownToWord(fullContent, `${title}.docx`);

      message.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      message.error("导出失败，请重试");
    } finally {
      exportLoading.value = false;
    }
  }

  // 更新PRD内容的方法，供父组件调用
  function updateContent(data: PrdData) {
    prdData.value = data;
  }

  // 更新sections内容的方法，供父组件调用
  function updateSections(newSections: Record<string, string>) {
    sections.value = { ...newSections };
  }

  const sseConnection = ref<{ disconnect: () => void } | null>(null);
  // 各个模块的生成处理方法
  async function handleGenerate(title: string, sectionKey: string) {
    try {
      const params = {
        type: sectionKey,
      };
      // 加载中
      loadingState[sectionKey] = true;
      sections.value[sectionKey] = "";
      // 使用EventSource流式生成指定类型文档
      sseConnection.value = generatePrdChart(props.currentProjectId, params, {
        onStart: () => {
          console.warn(`开始生成${title}文档...`);
        },
        onMessage: (content: string) => {
          // 累积内容
          sections.value[sectionKey] += content;
        },
        onCompleted: () => {
          loadingState[sectionKey] = false;
          // 生成完成，内容已更新到 sections.value[sectionKey]
          console.warn(`${title}生成完成`);
        },
        onError: (error: any) => {
          loadingState[sectionKey] = false;
          console.error(`生成${title}失败:`, error);
        },
      });
    } catch (_error) {
      loadingState[sectionKey] = false;
      console.error(`生成${title}失败:`, _error);
      message.error(`生成${title}失败，请重试`);
    }
  }

  // 断开SSE连接
  function disconnectSSE() {
    if (sseConnection.value) {
      console.warn("🔌 正在断开 SSE 连接...");
      sseConnection.value.disconnect();
      sseConnection.value = null;
      console.warn("✅ SSE 连接已清理");
    } else {
      console.warn("ℹ️ 没有活跃的 SSE 连接需要断开");
    }
  }

  // 处理主要内容重新生成
  function handleMainContentRegenerate() {
    // 通知父组件重新生成PRD主要内容
    emits("regenerateMainContent");
  }

  // 各个模块的导出功能
  function handleSectionExport(title: string, sectionKey: string) {
    exportMarkdownToWord(sections.value[sectionKey], `${displayTitle.value}-${title}.docx`);
  }

  // 滚动到指定章节
  function scrollToSection(sectionTitle: string) {
    // 等待DOM更新后再执行滚动
    nextTick(() => {
      // 基于配置数组动态生成映射关系
      const sectionIdMap: Record<string, string> = {
        // PRD正文的特殊映射关系
        PRD正文: "prd-content-main",
        概述: "prd-content-main",
        用户使用旅程: "prd-content-main",
        相关页面: "prd-content-main",
        功能详细描述: "prd-content-main",
      };

      // 从配置数组中动态添加映射关系
      contentConfigs.forEach((config) => {
        // 添加标题映射
        sectionIdMap[config.title] = config.id;
        // 添加带"生成"后缀的映射
        sectionIdMap[`${config.title} 生成`] = config.id;
      });

      const targetId = sectionIdMap[sectionTitle];
      if (targetId && scrollbarRef.value) {
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          // 计算目标元素的offsetTop位置
          const offsetTop = targetElement.offsetTop;

          // 使用NScrollbar的scrollTo方法
          scrollbarRef.value.scrollTo({ top: offsetTop, behavior: "smooth" });
        }
      }
    });
  }
  // 调用子组件的setRegenerateLoading方法的函数，并暴露给父组件
  function setMainContentRegenerateLoading(value: boolean) {
    mainContentRef.value?.setRegenerateLoading(value);
  }

  // 组件卸载时清理 SSE 连接
  onBeforeUnmount(() => {
    disconnectSSE();
  });

  // 暴露方法给父组件
  defineExpose({
    updateContent,
    updateSections,
    scrollToSection,
    setMainContentRegenerateLoading,
    disconnectSSE,
  });
</script>

<style scoped>
/* 自定义滚动条样式 */
:deep(.overflow-y-auto) {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

:deep(.overflow-y-auto):hover {
  scrollbar-color: #555 #f1f1f1;
}

/* 减小 MdPreview 中 h1 的上边距 - 针对 md-editor-v3 */
:deep(.md-editor-preview-wrapper h1) {
  margin-top: 0.5rem !important;
  margin-block-start: 0.5rem !important;
}

/* 如果需要调整其他标题的边距 */
:deep(.md-editor-preview-wrapper h2) {
  margin-top: 1rem !important;
  margin-block-start: 1rem !important;
}

:deep(.md-editor-preview-wrapper h3) {
  margin-top: 0.75rem !important;
  margin-block-start: 0.75rem !important;
}
</style>
