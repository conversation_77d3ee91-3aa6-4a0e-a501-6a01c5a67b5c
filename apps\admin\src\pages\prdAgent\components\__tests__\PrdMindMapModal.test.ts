import { describe, expect, it, vi } from "vitest";
import { mount } from "@vue/test-utils";
import PrdMindMapModal from "../PrdMindMapModal.vue";

// Mock Markmap 组件
vi.mock("~/component/Markmap", () => ({
  Markmap: {
    name: "Markma<PERSON>",
    props: ["content", "autoFit", "debounceDelay"],
    emits: ["ready", "error"],
    template: `
      <div class="mock-markmap" data-testid="markmap">
        <div v-if="content">{{ content.substring(0, 50) }}...</div>
        <div v-else>No content</div>
      </div>
    `,
  },
}));

// Mock NaiveUI
vi.mock("naive-ui", () => ({
  NModal: {
    name: "NModal",
    props: ["show", "maskClosable", "preset", "title", "class"],
    template: `
      <div v-if="show" class="mock-modal" data-testid="modal">
        <div class="modal-title">{{ title }}</div>
        <slot />
      </div>
    `,
  },
  useMessage: () => ({
    error: vi.fn(),
    success: vi.fn(),
    info: vi.fn(),
  }),
}));

describe("PrdMindMapModal", () => {
  const defaultProps = {
    show: true,
    prdData: {
      title: "测试PRD",
      content: `# 产品需求文档
## 功能概述
这是一个测试功能
### 详细说明
- 功能点1
- 功能点2
## 技术实现
使用Vue3开发`,
    },
  };

  it("应该正确渲染模态框", () => {
    const wrapper = mount(PrdMindMapModal, {
      props: defaultProps,
    });

    expect(wrapper.find('[data-testid="modal"]').exists()).toBe(true);
    expect(wrapper.find(".modal-title").text()).toBe("PRD思维导图");
  });

  it("应该正确传递 Markdown 内容给 Markmap 组件", () => {
    const wrapper = mount(PrdMindMapModal, {
      props: defaultProps,
    });

    const markmapComponent = wrapper.find('[data-testid="markmap"]');
    expect(markmapComponent.exists()).toBe(true);
    
    // 检查是否包含转换后的内容
    const content = markmapComponent.text();
    expect(content).toContain("测试PRD");
  });

  it("当没有内容时应该显示空状态", () => {
    const wrapper = mount(PrdMindMapModal, {
      props: {
        ...defaultProps,
        prdData: { title: "空PRD", content: "" },
      },
    });

    const markmapComponent = wrapper.find('[data-testid="markmap"]');
    expect(markmapComponent.text()).toBe("No content");
  });

  it("应该正确处理 PRD 内容转换", async () => {
    const wrapper = mount(PrdMindMapModal, {
      props: defaultProps,
    });

    // 获取组件实例来测试内部方法
    const vm = wrapper.vm as any;
    
    // 测试 Markdown 转换
    const markdownContent = vm.markdownContent;
    expect(markdownContent).toContain("# 测试PRD");
    expect(markdownContent).toContain("## 产品需求文档");
    expect(markdownContent).toContain("- 功能点1");
  });

  it("应该在模态框关闭时正确触发事件", async () => {
    const wrapper = mount(PrdMindMapModal, {
      props: defaultProps,
    });

    // 模拟关闭模态框
    await wrapper.setProps({ show: false });
    
    expect(wrapper.find('[data-testid="modal"]').exists()).toBe(false);
  });
});
