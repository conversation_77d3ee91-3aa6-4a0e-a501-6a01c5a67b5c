# PRD EventSource 封装工具使用说明

## 概述

`sse-helper.ts` 是专门为PRD功能设计的EventSource封装工具，基于项目现有的SseClient实现，支持POST请求和自定义请求头，提供了统一的SSE连接管理和事件处理机制。

## 主要功能

- 🔗 **连接管理**：自动处理连接建立、状态监控和资源清理
- 📡 **事件解析**：自动解析SSE数据格式（event和data字段）
- 🔄 **重连机制**：支持自动重连和重试策略
- 🎯 **类型安全**：完整的TypeScript类型定义
- ⚡ **状态管理**：实时连接状态监控

## 支持的事件类型

### PRDEventType 枚举

```typescript
export enum PRDEventType {
  PROJECT_CREATED = 'project_created',  // 项目创建事件
  MESSAGE = 'message',                  // 消息流事件
  MESSAGE_END = 'message_end',          // 消息结束事件
}
```

### 事件过滤机制

工具实现了严格的事件类型过滤机制：

1. **支持的事件类型**：只处理 `project_created`、`message`、`message_end` 三种事件
2. **过滤其他事件**：自动过滤 `workflow_started`、`node_started`、`node_finished` 等其他事件类型
3. **日志记录**：被过滤的事件会记录警告日志，便于调试
4. **性能优化**：避免处理不必要的事件，提高性能

### 消息格式处理

工具支持两种SSE消息格式：

#### 1. JSON格式（推荐）
```json
{
  "data": {
    "created_at": "2025-08-01 10:22:30",
    "description": "生成测试用例",
    "id": "23b7712d-e280-4f4f-b788-176dc61697d3",
    "requirements": "生成测试用例",
    "status": "draft",
    "title": "生成测试用例"
  },
  "event": "project_created"
}
```

#### 2. 传统SSE格式（兼容性）
```
event: project_created
data: {"id": "project-123", "title": "PRD标题"}
```

### 事件数据结构

```typescript
// 项目创建事件
interface ProjectCreatedData {
  data: {
    id: string;
    title?: string;
    description?: string;
    requirements?: string;
    status?: string;
    created_at?: string;
    [key: string]: any;
  };
  event: 'project_created';
}

// 消息事件
interface MessageData {
  answer: string;
  event: 'message';
  [key: string]: any;
}

// 消息结束事件
interface MessageEndData {
  event: 'message_end';
  [key: string]: any;
}
```

## 使用方法

### 1. 基本使用

```typescript
import { createPRDSSEConnection, SSEConnectionState } from './sse-helper';

const sseManager = createPRDSSEConnection(
  // 连接配置
  {
    url: '/api/v1/prd/projects',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-done-tenant': 'tenant-id'
    },
    body: { title: 'PRD标题', description: '描述' },
    timeout: 60000,
    retryAttempts: 3,
    retryDelay: 2000
  },
  // 事件处理器
  {
    onProjectCreated: (data) => {
      console.log('项目创建:', data.data.id);
    },
    onMessage: (data) => {
      console.log('收到消息:', data.answer);
    },
    onMessageEnd: () => {
      console.log('消息结束');
    }
  },
  // 状态回调
  {
    onStateChange: (state) => {
      console.log('连接状态变化:', state);
    },
    onError: (error) => {
      console.error('连接错误:', error);
    }
  }
);

// 建立连接
sseManager.connect();

// 手动断开连接
sseManager.disconnect();
```

### 2. 在API中使用

```typescript
// apis/internal/prd.ts
export function generatePrdStream(
  formData: PrdFormData,
  callbacks: {
    onStart?: () => void;
    onProjectCreated?: (projectId: string) => void;
    onMessage?: (content: string) => void;
    onCompleted?: () => void;
    onError?: (error: any) => void;
  }
): { disconnect: () => void } {
  const sseManager = createPRDSSEConnection(
    {
      url: API.GeneratePrd,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: formData,
    },
    {
      onProjectCreated: (data) => {
        callbacks.onProjectCreated?.(data.data.id);
      },
      onMessage: (data) => {
        callbacks.onMessage?.(data.answer);
      },
      onMessageEnd: () => {
        callbacks.onCompleted?.();
      }
    }
  );

  sseManager.connect();
  
  return {
    disconnect: () => sseManager.disconnect()
  };
}
```

### 3. 在组件中使用

```typescript
// 在Vue组件中
const sseConnection = ref<{ disconnect: () => void } | null>(null);

function startGeneration() {
  sseConnection.value = generatePrdStream(formData, {
    onStart: () => {
      console.log('开始生成');
    },
    onProjectCreated: (projectId) => {
      currentProjectId.value = projectId;
    },
    onMessage: (content) => {
      prdContent.value += content;
    },
    onCompleted: () => {
      console.log('生成完成');
    },
    onError: (error) => {
      console.error('生成失败:', error);
    }
  });
}

// 组件卸载时清理连接
onBeforeUnmount(() => {
  sseConnection.value?.disconnect();
});
```

## 连接状态

```typescript
enum SSEConnectionState {
  IDLE = 'idle',                    // 空闲状态
  CONNECTING = 'connecting',        // 连接中
  CONNECTED = 'connected',          // 已连接
  DISCONNECTED = 'disconnected',    // 已断开
  ERROR = 'error'                   // 错误状态
}
```

## 配置选项

### SSEConnectionConfig

```typescript
interface SSEConnectionConfig {
  url: string;                    // 请求URL
  method?: 'GET' | 'POST';       // 请求方法，默认POST
  headers?: Record<string, string>; // 请求头
  body?: any;                    // 请求体
  timeout?: number;              // 超时时间（毫秒），默认30000
  retryAttempts?: number;        // 重试次数，默认3
  retryDelay?: number;           // 重试延迟（毫秒），默认1000
}
```

## 错误处理

工具提供了完善的错误处理机制：

### 多层次错误捕获

1. **连接建立阶段**：
   - 捕获配置参数错误
   - 捕获网络请求初始化失败
   - 提供详细的错误日志和用户友好提示

2. **数据传输阶段**：
   - 捕获数据解析异常
   - 捕获SSE流处理错误
   - 自动进行错误恢复

3. **连接维护阶段**：
   - 监控连接状态异常
   - 处理服务器响应错误
   - 管理连接超时情况

### 错误处理特性

- **用户友好提示**：将技术错误转换为用户可理解的提示信息
- **详细错误日志**：记录完整的错误上下文，便于调试
- **自动重连机制**：网络错误时自动尝试重连，支持配置重试次数和延迟
- **资源清理**：错误发生时自动清理连接资源，避免内存泄露
- **状态同步**：确保连接状态与实际情况保持一致

### 错误类型处理

```typescript
// 网络连接错误
"接口连接失败，请检查网络或稍后重试"

// 服务器响应错误
"服务器响应错误 (500)，请稍后重试"

// 配置参数错误
"创建连接失败，请检查配置参数"

// 数据处理错误
"数据处理失败: [具体错误信息]"

// 连接超时
"SSE连接超时"
```

## 最佳实践

1. **及时清理连接**：在组件卸载时调用disconnect()
2. **合理设置超时**：根据业务需求设置合适的超时时间
3. **错误处理**：始终提供错误处理回调
4. **状态监控**：监听连接状态变化，提供用户反馈

## 技术实现说明

### 基于项目现有的SseClient

本工具基于项目现有的 `@celeris/request` 中的 `SseClient` 实现，具有以下优势：

1. **支持POST请求**：可以发送POST请求，不受原生EventSource限制
2. **支持自定义请求头**：可以添加Authorization、x-done-tenant等自定义头
3. **支持请求体**：可以在请求体中传递复杂数据结构
4. **流式处理**：基于fetch API的ReadableStream实现

### 与原生EventSource的区别

**原生EventSource限制：**
- ❌ 只支持GET请求
- ❌ 无法设置自定义请求头
- ❌ 只能通过URL参数传递数据

**项目SseClient优势：**
- ✅ 支持POST请求
- ✅ 支持自定义请求头
- ✅ 支持请求体传递数据
- ✅ 更好的错误处理

### 使用示例

```typescript
const sseManager = createPRDSSEConnection({
  url: '/api/v1/prd/projects',
  method: 'POST', // 支持POST请求
  headers: {
    'Content-Type': 'application/json',
    'x-done-tenant': 'tenant-123', // 支持自定义请求头
    'Authorization': 'Bearer token'
  },
  body: { // 支持请求体
    title: 'PRD标题',
    description: '描述',
    requirements: '需求详情'
  }
});
```

## 注意事项

- 仅限于PRD相关功能使用
- 基于项目现有的SseClient实现，保持技术栈一致性
- 自动重连有次数限制，避免无限重连
- 消息结束事件会自动关闭连接
- 支持完整的HTTP请求特性（POST、请求头、请求体）
