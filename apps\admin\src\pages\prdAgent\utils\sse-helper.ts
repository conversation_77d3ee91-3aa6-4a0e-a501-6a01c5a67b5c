/**
 * PRD功能专用的EventSource封装工具
 * 基于项目现有的SseClient实现，支持POST请求和自定义请求头
 * 提供统一的SSE连接管理和事件处理
 */

import { sseRequest } from "@celeris/request";

/**
 * SSE连接状态枚举
 */
export enum SSEConnectionState {
  IDLE = "idle", // 空闲状态
  CONNECTING = "connecting", // 连接中
  CONNECTED = "connected", // 已连接
  DISCONNECTED = "disconnected", // 已断开
  ERROR = "error", // 错误状态
}

/**
 * PRD相关的SSE事件类型
 */
export enum PRDEventType {
  PROJECT_CREATED = "project_created",
  MESSAGE = "message",
  MESSAGE_END = "message_end",
}

/**
 * 事件数据类型定义
 */
export interface ProjectCreatedData {
  data: {
    id: string;
    [key: string]: any;
  };
}

export interface MessageData {
  answer: string;
  [key: string]: any;
}

export interface MessageEndData {
  [key: string]: any;
}

/**
 * SSE事件处理器接口
 */
export interface SSEEventHandlers {
  onProjectCreated?: (data: ProjectCreatedData) => void;
  onMessage?: (data: MessageData) => void;
  onMessageEnd?: (data: MessageEndData) => void;
  onUnknownEvent?: (eventType: string, data: any) => void;
}

/**
 * SSE连接配置接口
 */
export interface SSEConnectionConfig {
  url: string;
  method?: "GET" | "POST" | "PUT";
  headers?: Record<string, string>;
  body?: any;
  timeout?: number; // 连接超时时间（毫秒）
  retryAttempts?: number; // 重连尝试次数
  retryDelay?: number; // 重连延迟时间（毫秒）
}

/**
 * SSE连接状态回调接口
 */
export interface SSEStateCallbacks {
  onStateChange?: (state: SSEConnectionState) => void;
  onError?: (error: Error) => void;
  onTimeout?: () => void;
  onRetry?: (attempt: number) => void;
}

/**
 * PRD专用EventSource连接管理器
 * 基于项目现有的SseClient实现
 */
export class PRDSSEManager {
  private state: SSEConnectionState = SSEConnectionState.IDLE;
  private config: SSEConnectionConfig;
  private eventHandlers: SSEEventHandlers;
  private stateCallbacks: SSEStateCallbacks;
  private retryCount = 0;
  private timeoutId: number | null = null;
  private connectionActive = false;
  private abortController: AbortController | null = null;

  constructor(
    config: SSEConnectionConfig,
    eventHandlers: SSEEventHandlers,
    stateCallbacks: SSEStateCallbacks = {},
  ) {
    this.config = {
      method: "POST",
      timeout: 30000, // 默认30秒超时
      retryAttempts: 3, // 默认重试3次
      retryDelay: 1000, // 默认延迟1秒
      ...config,
    };
    this.eventHandlers = eventHandlers;
    this.stateCallbacks = stateCallbacks;
  }

  /**
   * 建立SSE连接
   */
  public connect(): void {
    if (this.state === SSEConnectionState.CONNECTING || this.state === SSEConnectionState.CONNECTED) {
      console.warn("SSE连接已存在，无需重复连接");
      return;
    }

    this.setState(SSEConnectionState.CONNECTING);
    this.setupTimeout();
    this.connectionActive = true;

    // 创建新的 AbortController 用于取消请求
    this.abortController = new AbortController();

    try {
      const { url, headers, body, method } = this.config;

      // 根据配置的方法选择合适的请求方式
      const requestOptions = {
        headers: {
          "Content-Type": "application/json",
          ...headers,
        },
        body,
      };

      const callbacks = {
        onStart: () => {
          this.clearTimeout();
          this.retryCount = 0;
          this.setState(SSEConnectionState.CONNECTED);
        },
        onData: (data: string) => {
          try {
            this.handleRawMessage(data);
          } catch (dataError) {
            console.error("处理SSE数据时发生错误:", dataError);
            this.handleError(new Error(`数据处理失败: ${dataError}`));
          }
        },
        onCompleted: () => {
          this.setState(SSEConnectionState.DISCONNECTED);
          this.connectionActive = false;
        },
        onError: (error: any) => {
          console.error("SSE连接发生错误:", error);

          // 构建用户友好的错误信息
          let errorMessage = "接口连接失败，请检查网络或稍后重试";
          if (error && typeof error === "object") {
            if (error.status) {
              errorMessage = `服务器响应错误 (${error.status})，请稍后重试`;
            } else if (error.message) {
              errorMessage = `连接失败: ${error.message}`;
            }
          } else if (typeof error === "string") {
            errorMessage = `连接失败: ${error}`;
          }

          // 主动断开连接，避免资源泄露
          this.connectionActive = false;
          this.clearTimeout();

          // 统一处理错误状态
          this.handleError(new Error(errorMessage));
        },
      };

      // 使用增强的 SSE 请求方法，支持 AbortController
      this.createAbortableSSERequest(url, {
        method: method || "POST",
        ...requestOptions,
      }, callbacks);
    } catch (error) {
      console.error("创建SSE连接时发生异常:", error);

      // 主动断开连接，避免资源泄露
      this.connectionActive = false;
      this.clearTimeout();

      // 构建详细的错误信息
      const errorMessage = error instanceof Error
        ? `创建连接失败: ${error.message}`
        : "创建连接失败，请检查配置参数";

      // 统一处理错误状态
      this.handleError(new Error(errorMessage));
    }
  }

  /**
   * 手动关闭连接
   */
  public disconnect(): void {
    console.warn("🔌 手动断开 SSE 连接...");

    this.connectionActive = false;
    this.clearTimeout();

    // 取消正在进行的请求
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
      console.warn("✅ 已取消 SSE 请求");
    }

    this.setState(SSEConnectionState.DISCONNECTED);
    console.warn("🔌 SSE 连接已断开");
  }

  /**
   * 获取当前连接状态
   */
  public getState(): SSEConnectionState {
    return this.state;
  }

  /**
   * 检查是否已连接
   */
  public isConnected(): boolean {
    return this.state === SSEConnectionState.CONNECTED;
  }

  /**
   * 处理原始消息数据
   */
  private handleRawMessage(data: string): void {
    try {
      // 首先尝试直接解析JSON格式的消息
      if (data.trim().startsWith("{")) {
        try {
          const messageObj = JSON.parse(data.trim());

          // 检查消息是否包含event字段
          if (messageObj.event) {
            // 验证事件类型是否为支持的类型
            const supportedEvents = [PRDEventType.PROJECT_CREATED, PRDEventType.MESSAGE, PRDEventType.MESSAGE_END];

            if (supportedEvents.includes(messageObj.event)) {
              // 处理支持的事件类型
              this.handleTypedEvent(messageObj.event, JSON.stringify(messageObj));
            } else {
              // 过滤不支持的事件类型
              console.warn(`过滤不支持的事件类型: ${messageObj.event}`, messageObj);
            }
          } else {
            console.warn("SSE消息缺少event字段:", messageObj);
          }
          return;
        } catch (jsonError) {
          // JSON解析失败，继续尝试传统SSE格式解析
          console.warn("JSON格式解析失败，尝试传统SSE格式:", jsonError);
        }
      }

      // 传统SSE格式解析（兼容性处理）
      const lines = data.split("\n");
      let eventType = "";
      let eventData = "";

      for (const line of lines) {
        if (line.startsWith("event:")) {
          eventType = line.substring(6).trim();
        } else if (line.startsWith("data:")) {
          eventData = line.substring(5).trim();
        }
      }

      if (eventType && eventData) {
        // 验证事件类型是否为支持的类型
        const supportedEvents = [PRDEventType.PROJECT_CREATED, PRDEventType.MESSAGE, PRDEventType.MESSAGE_END];

        if (supportedEvents.includes(eventType as PRDEventType)) {
          console.warn(`处理支持的传统SSE事件类型: ${eventType}`);
          this.handleTypedEvent(eventType, eventData);
        } else {
          console.warn(`过滤不支持的传统SSE事件类型: ${eventType}`, { eventType, eventData });
        }
      } else if (data.trim()) {
        // 如果没有明确的事件类型，尝试作为message事件处理
        console.warn("未识别的消息格式，尝试作为message事件处理:", data);
        this.handleTypedEvent(PRDEventType.MESSAGE, data);
      }
    } catch (error) {
      console.error("解析SSE消息失败:", error, data);
    }
  }

  /**
   * 处理特定类型的事件
   */
  private handleTypedEvent(eventType: string, data: string): void {
    // 验证事件类型是否为支持的类型
    const supportedEvents = [PRDEventType.PROJECT_CREATED, PRDEventType.MESSAGE, PRDEventType.MESSAGE_END];

    if (!supportedEvents.includes(eventType as PRDEventType)) {
      console.warn(`事件类型验证失败，过滤不支持的事件: ${eventType}`);
      return;
    }

    try {
      const parsedData = JSON.parse(data);

      switch (eventType) {
        case PRDEventType.PROJECT_CREATED:
          console.warn(`处理项目创建事件: ${eventType}`);
          this.eventHandlers.onProjectCreated?.(parsedData as ProjectCreatedData);
          break;

        case PRDEventType.MESSAGE:
          this.eventHandlers.onMessage?.(parsedData as MessageData);
          break;

        case PRDEventType.MESSAGE_END:
          console.warn(`处理消息结束事件: ${eventType}`);
          this.eventHandlers.onMessageEnd?.(parsedData as MessageEndData);
          // 消息结束时自动关闭连接
          this.disconnect();
          break;

        default:
          // 理论上不会到达这里，因为上面已经过滤了
          console.warn("意外的事件类型到达default分支:", eventType, parsedData);
          this.eventHandlers.onUnknownEvent?.(eventType, parsedData);
      }
    } catch (error) {
      console.error("解析事件数据失败:", error, data);
    }
  }

  /**
   * 设置连接超时
   */
  private setupTimeout(): void {
    if (this.config.timeout && this.config.timeout > 0) {
      this.timeoutId = window.setTimeout(() => {
        this.handleTimeout();
      }, this.config.timeout);
    }
  }

  /**
   * 清除超时定时器
   */
  private clearTimeout(): void {
    if (this.timeoutId) {
      window.clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * 处理连接超时
   */
  private handleTimeout(): void {
    this.stateCallbacks.onTimeout?.();
    this.handleError(new Error("SSE连接超时"));
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    console.error("SSE连接处理错误:", {
      error: error.message,
      state: this.state,
      retryCount: this.retryCount,
      maxRetries: this.config.retryAttempts || 0,
      connectionActive: this.connectionActive,
    });

    // 设置错误状态
    this.setState(SSEConnectionState.ERROR);

    // 确保连接状态正确更新
    this.connectionActive = false;
    this.clearTimeout();

    // 通知上层组件错误信息
    this.stateCallbacks.onError?.(error);

    // 尝试重连
    if (this.retryCount < (this.config.retryAttempts || 0)) {
      console.warn(`SSE连接失败，准备第${this.retryCount + 1}次重连...`);
      this.retryConnection();
    } else {
      console.error("SSE连接重试次数已达上限，停止重连");
      // 最终失败时设置为断开状态
      this.setState(SSEConnectionState.DISCONNECTED);
    }
  }

  /**
   * 重连逻辑
   */
  private retryConnection(): void {
    this.retryCount++;

    console.warn(`开始第${this.retryCount}次重连尝试，延迟${this.config.retryDelay || 1000}ms`);
    this.stateCallbacks.onRetry?.(this.retryCount);

    setTimeout(() => {
      try {
        // 确保在重连前清理之前的状态
        this.clearTimeout();

        // 检查是否仍需要重连（可能在延迟期间被手动断开）
        if (this.connectionActive) {
          console.warn(`执行第${this.retryCount}次重连...`);
          this.connect();
        } else {
          console.warn("重连已被取消，连接已手动断开");
        }
      } catch (retryError) {
        console.error("重连过程中发生异常:", retryError);
        // 重连失败时，确保状态正确
        this.connectionActive = false;
        this.setState(SSEConnectionState.DISCONNECTED);
      }
    }, this.config.retryDelay || 1000);
  }

  /**
   * 创建支持 AbortController 的 SSE 请求
   */
  private createAbortableSSERequest(
    url: string,
    options: RequestInit,
    callbacks: {
      onStart?: () => void;
      onData?: (data: string) => void;
      onCompleted?: () => void;
      onError?: (error: any) => void;
    }
  ): void {
    const { body } = options;
    if (body) {
      options.body = JSON.stringify(body);
    }

    // 添加 AbortController 的 signal
    if (this.abortController) {
      options.signal = this.abortController.signal;
    }

    fetch(url, options)
      .then((res: Response) => {
        if (!/^(2|3)\d{2}$/.test(res.status.toString())) {
          callbacks.onError?.("Server Error");
          return;
        }
        return this.handleAbortableStream(res, callbacks);
      })
      .catch((e) => {
        // 检查是否是用户主动取消的请求
        if (e.name === 'AbortError') {
          console.warn("SSE 请求被用户取消");
          return;
        }
        callbacks.onError?.(e);
      });
  }

  /**
   * 处理支持中断的流数据
   */
  private handleAbortableStream(
    response: Response,
    callbacks: {
      onStart?: () => void;
      onData?: (data: string) => void;
      onCompleted?: () => void;
      onError?: (error: any) => void;
    }
  ): void {
    if (!response.ok) {
      throw new Error("接口请求失败，请稍后重试！");
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";

    const read = () => {
      // 检查连接是否仍然活跃
      if (!this.connectionActive) {
        reader?.cancel();
        return;
      }

      let hasError = false;
      reader?.read().then((result: any) => {
        if (result.done) {
          callbacks.onCompleted?.();
          return;
        }

        buffer += decoder.decode(result.value, { stream: true });
        const lines = buffer.split("\n");

        try {
          lines.forEach((message) => {
            if (message.startsWith("data: ")) {
              callbacks.onData?.(message.slice(6));
            }
          });
          buffer = lines[lines.length - 1];
        } catch (e) {
          callbacks.onData?.("");
          hasError = true;
          callbacks.onCompleted?.(true);
          return;
        }

        if (!hasError && this.connectionActive) {
          read();
        }
      }).catch((error) => {
        if (error.name === 'AbortError') {
          console.warn("流读取被中断");
          return;
        }
        callbacks.onError?.(error);
      });
    };

    callbacks.onStart?.();
    read();
  }

  /**
   * 设置连接状态
   */
  private setState(newState: SSEConnectionState): void {
    if (this.state !== newState) {
      this.state = newState;
      this.stateCallbacks.onStateChange?.(newState);
    }
  }
}

/**
 * 创建PRD专用的SSE连接
 * 便捷方法，用于快速创建和管理SSE连接
 */
export function createPRDSSEConnection(
  config: SSEConnectionConfig,
  eventHandlers: SSEEventHandlers,
  stateCallbacks?: SSEStateCallbacks,
): PRDSSEManager {
  return new PRDSSEManager(config, eventHandlers, stateCallbacks);
}
