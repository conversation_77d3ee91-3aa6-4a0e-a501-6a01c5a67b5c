import type { RouteRecordRaw } from "vue-router";
import { LAYOUT } from "~/router/constant";

const admin: RouteRecordRaw = {
  path: "/agent",
  name: "agent",
  component: LAYOUT,
  redirect: "/agent/behavior",
  meta: {
    title: "端到端智能体",
    icon: "i-tabler-brand-denodo",
    orderNumber: 7,
    // shouldVerifyVisiblePermission: false,
  },
  children: [
    {
      path: "behavior",
      name: "behavior",
      component: () => import("~/pages/agent/UserBehavior.vue"),
      meta: {
        title: "行为分析",
        icon: "i-token-branded:nxm",
        breadcrumb: true,
      }
    },
    {
      path: "codeReview",
      name: "codeReview",
      component: () => import("~/pages/codeReview/index.vue"),
      meta: {
        title: "代码评审",
        icon: "i-token-branded:crts",
        breadcrumb: true,
      }
    },
    {
      path: "prdAgent",
      name: "prdAgent",
      component: () => import("~/pages/prdAgent/index.vue"),
      meta: {
        title: "PRD智能体",
        icon: "i-token-branded:pxp",
        breadcrumb: true,
      }
    }
  ],
};

export default admin;
