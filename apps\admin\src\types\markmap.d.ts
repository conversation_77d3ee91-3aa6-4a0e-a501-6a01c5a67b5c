// Markmap 类型定义
declare module "markmap-lib" {
  export interface INode {
    type: string;
    depth: number;
    value?: string;
    children?: INode[];
    payload?: any;
  }

  export interface ITransformResult {
    root: INode;
    features: any;
  }

  export class Transformer {
    constructor();
    transform(markdown: string): ITransformResult;
  }

  export const builtInPlugins: any[];
  export function patchCSSItem(item: any): any;
  export function patchJSItem(item: any): any;
  export const transformerVersions: any;
}

declare module "markmap-view" {
  export interface IMarkmapOptions {
    color?: (node: any) => string;
    duration?: number;
    maxWidth?: number;
    paddingX?: number;
    paddingY?: number;
    spacingVertical?: number;
    spacingHorizontal?: number;
    autoFit?: boolean;
    pan?: boolean;
    zoom?: boolean;
  }

  export class Markmap {
    static create(
      element: HTMLElement,
      options?: IMarkmapOptions
    ): Markmap;

    setData(root: any): void;
    fit(): void;
    destroy(): void;
  }

  // 支持默认导出
  const _default: {
    Markmap: typeof Markmap;
  };
  export default _default;
}
