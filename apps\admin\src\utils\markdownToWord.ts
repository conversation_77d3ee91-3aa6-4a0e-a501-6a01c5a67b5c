// import domtoimage from "dom-to-image-more";
import { saveAs } from "file-saver";
import { asBlob } from "html-docx-js-typescript";
import { marked } from "marked";
import mermaid from "mermaid";

export async function exportMarkdownToWord(markdown: string, filename = "导出文档.docx") {
  // 1. 将 Markdown 转为 HTML
  let rawHtml = await marked(markdown);

  rawHtml = rawHtml.replace(/<li>\s*<p><strong>(.*?)<\/strong><\/p>/g, "<li><strong>$1</strong>");

  // 2. 创建容器解析 HTML
  const container = document.createElement("div");
  container.innerHTML = rawHtml;

  // 3. 渲染所有 mermaid 代码块为 SVG
  const mermaidBlocks = container.querySelectorAll("code.language-mermaid");
  const renderPromises = Array.from(mermaidBlocks).map(async (block) => {
    const code = block.textContent || "";
    const id = `mermaid-${Math.random().toString(36).substring(2)}`;
    try {
      const { svg } = await mermaid.render(id, code);
      // 渲染 SVG 到临时 DOM，用于转 PNG
      const svgContainer = document.createElement("div");
      svgContainer.innerHTML = svg;
      document.body.appendChild(svgContainer); // 必须插入 DOM 才能渲染

      // 转 PNG
      const svgElement = svgContainer.querySelector("svg");
      if (!svgElement) {
        throw new Error("SVG元素不存在");
      }

      // 使用内联SVG数据URL方法解决安全问题
      // 将SVG序列化为字符串并进行编码
      const svgString = new XMLSerializer().serializeToString(svgElement);
      const encodedSvg = encodeURIComponent(svgString)
        .replace(/'/g, "%27")
        .replace(/"/g, "%22");

      // 获取SVG的viewBox以确保正确的尺寸比例
      const viewBox = svgElement.getAttribute("viewBox") || "";
      const [, , vbWidth, vbHeight] = viewBox.split(" ").map(Number);

      // 确定图像尺寸
      const width = vbWidth || 700;
      const height = vbHeight || 600;

      // 使用foreignObject方式创建可安全绘制的图像
      const pngData = await new Promise<string>((resolve, reject) => {
        try {
          // 创建一个新的canvas元素
          const canvas = document.createElement("canvas");
          const scale = 3; // 放大3倍以提高清晰度
          canvas.width = width * scale;
          canvas.height = height * scale;

          // 使用内联的Data URL方式创建图像
          const img = new Image();
          img.onload = () => {
            try {
              const ctx = canvas.getContext("2d");
              if (!ctx) {
                reject(new Error("无法创建Canvas上下文"));
                return;
              }

              // 填充白色背景
              ctx.fillStyle = "#FFFFFF";
              ctx.fillRect(0, 0, canvas.width, canvas.height);

              // 缩放并绘制图像
              ctx.scale(scale, scale);
              ctx.drawImage(img, 0, 0, width, height);

              // 导出为PNG
              const pngUrl = canvas.toDataURL("image/png", 1.0);
              resolve(pngUrl);
            } catch (err) {
              reject(err);
            }
          };

          img.onerror = (e) => {
            console.error("图像加载失败:", e);
            reject(new Error("SVG图像加载失败"));
          };

          // 直接使用data URL方式加载SVG - 解决跨域问题
          img.src = `data:image/svg+xml;charset=utf-8,${encodedSvg}`;
        } catch (err) {
          reject(err);
        }
      });

      // 移除临时SVG容器
      document.body.removeChild(svgContainer);

      // 创建 <img> 标签替代原 Mermaid 图
      const imgElement = document.createElement("img");
      imgElement.src = pngData;

      // 设置较大的固定尺寸以确保在Word中显示不会太小
      const minWidth = 600; // 设置最小宽度为600像素
      const aspectRatio = height / width || 1; // 使用实际SVG尺寸计算比例
      const calculatedHeight = Math.round(minWidth * aspectRatio);

      // 设置内联样式 - 确保在导出时保持这些属性
      imgElement.style.cssText = `
        display: block;
        width: ${minWidth}px;
        height: ${calculatedHeight}px;
        margin: 20px auto;
        border: none;
        padding: 0;
        outline: none;
        box-shadow: none;
        background: transparent;
      `;

      imgElement.alt = "Mermaid Diagram";

      // 添加明确的属性，确保Word正确处理图像大小
      imgElement.setAttribute("width", String(minWidth));
      imgElement.setAttribute("height", String(calculatedHeight));
      imgElement.setAttribute("border", "0");

      // 替换原始代码块
      const pre = block.parentElement?.tagName === "PRE" ? block.parentElement : block;
      if (pre && pre.parentElement) {
        pre.parentElement.replaceChild(imgElement, pre);
      }

      return imgElement;
    } catch (err) {
      console.error("Mermaid render error:", err);
      return null;
    }
  });

  // 等待所有图表渲染完成
  await Promise.all(renderPromises);

  // 4. 转换为 Word Buffer
  // const html = container.innerHTML;
  // const buffer = await htmlToDocx(html, null, {
  //   orientation: "portrait",
  //   margins: { top: 720, bottom: 720, left: 720, right: 720 },
  // });
  // 4. 使用 html-docx-js-typescript 转换
  const htmlWithWrapper = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { 
            font-family: "Microsoft YaHei", sans-serif; 
            font-size: 10.5pt;
            line-height: 1.6;
          }
          h1, h2, h3 { color: #2e6c80; }
          img { 
            min-width: 600px; /* 设置最小宽度 */
            max-width: 100%; 
            height: auto !important; /* 强制高度自适应 */
            display: block;
            margin: 20px auto;
            page-break-inside: avoid; /* 防止图片在Word文档中跨页 */
            -ms-interpolation-mode: bicubic; /* IE专用，提高图像质量 */
          }
          pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
          }
          code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
          }
          table {
            border-collapse: collapse;
            font-size: 9pt;
          }
        </style>
      </head>
      <body>${container.innerHTML}</body>
    </html>
  `;
  // 增加文档导出配置选项 - 仅使用库支持的选项
  const docxBlob = await asBlob(htmlWithWrapper, {
    orientation: "portrait",
    margins: {
      top: 1440, // Word中1英寸 = 1440单位
      right: 1440,
      bottom: 1440,
      left: 1440,
    },
  });
  saveAs(docxBlob, filename);

  // 5. 生成并下载 Word 文件
  // const blob = new Blob([buffer], {
  //   type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  // });
  // const link = document.createElement("a");
  // link.href = URL.createObjectURL(blob);
  // link.download = filename;
  // link.click();
  // URL.revokeObjectURL(link.href);
}
