/**
 * Markdown处理工具集
 * 提供Markdown到HTML转换、表格处理、列表处理和Word文档导出功能
 */

/**
 * 将Markdown表格转换为HTML表格
 * @param html - 包含Markdown表格的HTML字符串
 * @returns 转换后的HTML字符串
 */
export function convertMarkdownTables(html: string): string {
  const tableRegex = /(\|[^\n\r|\u2028\u2029]*\|.*\n)+/g;
  return html.replace(tableRegex, (match) => {
    const rows = match.trim().split("\n");
    if (rows.length < 2) {
      return match;
    }

    let tableHtml = "<table>";

    // 处理表头
    const headerRow = rows[0];
    const headers = headerRow.split("|").map(cell => cell.trim()).filter(cell => cell);
    if (headers.length > 0) {
      tableHtml += "<thead><tr>";
      headers.forEach((header) => {
        tableHtml += `<th>${header}</th>`;
      });
      tableHtml += "</tr></thead>";
    }

    // 跳过分隔行（第二行）
    if (rows.length > 2) {
      tableHtml += "<tbody>";
      for (let i = 2; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.split("|").map(cell => cell.trim()).filter(cell => cell);
        if (cells.length > 0) {
          tableHtml += "<tr>";
          cells.forEach((cell) => {
            tableHtml += `<td>${cell}</td>`;
          });
          tableHtml += "</tr>";
        }
      }
      tableHtml += "</tbody>";
    }

    tableHtml += "</table>";
    return tableHtml;
  });
}

/**
 * 将Markdown列表转换为HTML列表
 * @param html - 包含Markdown列表的HTML字符串
 * @returns 转换后的HTML字符串
 */
export function convertMarkdownLists(html: string): string {
  // 处理无序列表
  html = html.replace(/^[*\-] (.+)$/gm, "<li>$1</li>");
  // 处理有序列表
  html = html.replace(/^\d+\. (.+)$/gm, "<li>$1</li>");

  // 将连续的li标签包装在ul或ol中
  html = html.replace(/(<li>.*<\/li>\s*)+/gs, (match) => {
    return `<ul>${match}</ul>`;
  });

  return html;
}

/**
 * 将Markdown文本转换为HTML
 * @param markdown - Markdown格式的文本
 * @returns Promise<string> - 转换后的HTML字符串
 */
export async function convertMarkdownToHtml(markdown: string): Promise<string> {
  // 预处理：分离代码块，避免在代码块内进行转换
  const codeBlocks: string[] = [];
  const processedMarkdown = markdown.replace(/```([\s\S]*?)```/g, (_, code) => {
    const index = codeBlocks.length;
    codeBlocks.push(code);
    return `__CODE_BLOCK_${index}__`;
  });

  // 使用正则表达式转换常见的Markdown语法
  let html = processedMarkdown
    // 标题转换（支持更多级别）
    .replace(/^#### (.*$)/gm, "<h4>$1</h4>")
    .replace(/^### (.*$)/gm, "<h3>$1</h3>")
    .replace(/^## (.*$)/gm, "<h2>$1</h2>")
    .replace(/^# (.*$)/gm, "<h1>$1</h1>")
    // 图片（需要特殊处理）
    .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (_, alt, src) => {
      // 检查是否是base64图片或网络图片
      if (src.startsWith("data:") || src.startsWith("http")) {
        return `<img src="${src}" alt="${alt}" style="max-width: 100%; height: auto;" />`;
      } else {
        // 对于本地图片，显示占位符
        return `<p><strong>[图片: ${alt || src}]</strong></p>`;
      }
    })
    // 链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, "<a href=\"$2\">$1</a>")
    // 粗体和斜体
    .replace(/\*\*\*(.*?)\*\*\*/g, "<strong><em>$1</em></strong>")
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
    .replace(/\*(.*?)\*/g, "<em>$1</em>")
    // 删除线
    .replace(/~~(.*?)~~/g, "<del>$1</del>")
    // 行内代码
    .replace(/`(.*?)`/g, "<code>$1</code>")
    // 水平线
    .replace(/^---$/gm, "<hr>")
    .replace(/^\*\*\*$/gm, "<hr>");

  // 处理表格
  html = convertMarkdownTables(html);

  // 处理列表（需要更复杂的逻辑）
  html = convertMarkdownLists(html);

  // 恢复代码块
  codeBlocks.forEach((code, index) => {
    html = html.replace(`__CODE_BLOCK_${index}__`, `<pre><code>${code}</code></pre>`);
  });

  // 处理段落
  html = html
    .replace(/\n\n/g, "</p><p>")
    .replace(/\n/g, "<br>");

  // 包装在段落标签中，但避免在已有块级元素中包装
  const blockElements = /<(?:h[1-6]|div|p|ul|ol|li|table|tr|td|th|pre|hr)[^>]*>/i;
  if (!blockElements.test(html)) {
    html = `<p>${html}</p>`;
  }

  return html;
}

/**
 * Word文档导出选项接口
 */
export interface WordExportOptions {
  /** 文档标题 */
  title?: string;
  /** 字体系列 */
  fontFamily?: string;
  /** 基础字体大小 */
  fontSize?: string;
  /** 行高 */
  lineHeight?: number;
}

/**
 * 将HTML内容转换为Word文档并下载
 * @param html - HTML内容
 * @param filename - 文件名（不包含扩展名）
 * @param options - 导出选项
 * @returns Promise<void>
 */
export async function convertHtmlToDocx(
  html: string, 
  filename: string, 
  options: WordExportOptions = {}
): Promise<void> {
  const {
    title = filename,
    fontFamily = "'Microsoft YaHei', Arial, sans-serif",
    fontSize = "12pt",
    lineHeight = 1.6
  } = options;

  // 创建一个兼容Word的HTML文档结构
  const wordHtml = `
    <!DOCTYPE html>
    <html xmlns:o="urn:schemas-microsoft-com:office:office"
          xmlns:w="urn:schemas-microsoft-com:office:word"
          xmlns="http://www.w3.org/TR/REC-html40">
    <head>
      <meta charset="utf-8">
      <title>${title}</title>
      <!--[if gte mso 9]>
      <xml>
        <w:WordDocument>
          <w:View>Print</w:View>
          <w:Zoom>90</w:Zoom>
          <w:DoNotPromptForConvert/>
          <w:DoNotShowInsertionsAndDeletions/>
        </w:WordDocument>
      </xml>
      <![endif]-->
      <style>
        body { font-family: ${fontFamily}; font-size: ${fontSize}; line-height: ${lineHeight}; }
        h1 { font-size: 18pt; font-weight: bold; margin: 20pt 0 10pt 0; }
        h2 { font-size: 16pt; font-weight: bold; margin: 16pt 0 8pt 0; }
        h3 { font-size: 14pt; font-weight: bold; margin: 12pt 0 6pt 0; }
        h4 { font-size: 13pt; font-weight: bold; margin: 10pt 0 5pt 0; }
        p { margin: 6pt 0; }
        ul, ol { margin: 6pt 0; padding-left: 20pt; }
        li { margin: 3pt 0; }
        code { background-color: #f5f5f5; padding: 2pt 4pt; font-family: 'Courier New', monospace; }
        pre { background-color: #f5f5f5; padding: 8pt; margin: 6pt 0; font-family: 'Courier New', monospace; }
        table { border-collapse: collapse; width: 100%; margin: 6pt 0; }
        th, td { border: 1pt solid #ddd; padding: 6pt; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        img { max-width: 100%; height: auto; }
        hr { border: none; border-top: 1pt solid #ddd; margin: 12pt 0; }
        del { text-decoration: line-through; }
      </style>
    </head>
    <body>
      ${html}
    </body>
    </html>
  `;

  // 创建Blob并下载
  const blob = new Blob([wordHtml], {
    type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  });

  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = `${filename.replace(/[<>:"/\\|?*]/g, "_")}.doc`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * 将完整的Markdown内容导出为Word文档
 * @param markdown - Markdown内容
 * @param filename - 文件名
 * @param options - 导出选项
 * @returns Promise<void>
 */
export async function exportMarkdownToWord(
  markdown: string,
  filename: string,
  options: WordExportOptions = {}
): Promise<void> {
  const html = await convertMarkdownToHtml(markdown);
  await convertHtmlToDocx(html, filename, options);
}
